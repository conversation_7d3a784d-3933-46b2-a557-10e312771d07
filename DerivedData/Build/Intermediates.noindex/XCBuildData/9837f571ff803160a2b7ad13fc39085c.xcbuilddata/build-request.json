{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "668b8731326a427b882a522c7f7dd66e21e9fee47f373315123649226661169e"}], "containerPath": "/Users/<USER>/Desktop/learnDemo/learnDemo.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.2", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/learnDemo/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/learnDemo/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/learnDemo/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "26.0", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "CLANG_COVERAGE_MAPPING": "YES", "CLANG_PROFILE_DATA_DIRECTORY": "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/ProfileData", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "59", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "6018D587-43BD-4713-BD8A-1A83FAE03F69", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "26.0", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}