{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo": {"is-mutated": true}, "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/_CodeSignature", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib", "/Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>", "<target-learnDemo-****************************************************************--begin-scanning>", "<target-learnDemo-****************************************************************--end>", "<target-learnDemo-****************************************************************--linker-inputs-ready>", "<target-learnDemo-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/_CodeSignature", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Assets.car", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_signature", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/PkgInfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.hmap"], "roots": ["/tmp/learnDemo.dst", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products"], "outputs": ["<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo-668b8731326a427b882a522c7f7dd66e-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/learnDemo/learnDemo.xcodeproj", "signature": "55479dfb9aa6781d61b6d654469f8ce1"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo-668b8731326a427b882a522c7f7dd66e-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-learnDemo-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Metadata.appintents>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList"], "outputs": ["<target-learnDemo-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-ChangePermissions>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-StripSymbols>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-GenerateStubAPI>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ProductPostprocessingTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-CodeSign>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-Validate>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["<target-learnDemo-****************************************************************--<PERSON>ier-RegisterProduct>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-CopyAside>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-learnDemo-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["<target-learnDemo-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-learnDemo-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--GeneratedFilesTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ProductStructureTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-learnDemo-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--RealityAssetsTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.hmap"], "outputs": ["<target-learnDemo-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/PkgInfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist"], "outputs": ["<target-learnDemo-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--HeadermapTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleMapTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-learnDemo-****************************************************************--InfoPlistTaskProducer>", "<target-learnDemo-****************************************************************--VersionPlistTaskProducer>", "<target-learnDemo-****************************************************************--SanitizerTaskProducer>", "<target-learnDemo-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-learnDemo-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-learnDemo-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-learnDemo-****************************************************************--StubBinaryTaskProducer>", "<target-learnDemo-****************************************************************--TestTargetTaskProducer>", "<target-learnDemo-****************************************************************--TestHostTaskProducer>", "<target-learnDemo-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-learnDemo-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-learnDemo-****************************************************************--DocumentationTaskProducer>", "<target-learnDemo-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--start>", "<target-learnDemo-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["<target-learnDemo-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["<target-learnDemo-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ProductPostprocessingTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-learnDemo-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-learnDemo-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Assets.car", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_signature", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-learnDemo-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-learnDemo-****************************************************************--generated-headers>"]}, "P0:::Gate target-learnDemo-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h"], "outputs": ["<target-learnDemo-****************************************************************--swift-generated-headers>"]}, "P0:target-learnDemo-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Metadata.appintents>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ssu", "--bundle-id", "com.jack.learnDemo", "--product-path", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "a3c04c753afb51d4a2f0960cac0d9c37"}, "P0:target-learnDemo-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist/", "/Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings/", "/Users/<USER>/Desktop/learnDemo/Localizable.xcstrings/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/CameraView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal/", "/Users/<USER>/Desktop/learnDemo/learnDemo/ContentView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/PhotoPreviewView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/learnDemoApp.swift/", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo normal>", "<TRIGGER: MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"]}, "P0:target-learnDemo-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist/", "/Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings/", "/Users/<USER>/Desktop/learnDemo/Localizable.xcstrings/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/CameraView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal/", "/Users/<USER>/Desktop/learnDemo/learnDemo/ContentView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/PhotoPreviewView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/learnDemoApp.swift/", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>", "<target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>"]}, "P0:target-learnDemo-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist/", "/Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings/", "/Users/<USER>/Desktop/learnDemo/Localizable.xcstrings/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/CameraView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal/", "/Users/<USER>/Desktop/learnDemo/learnDemo/ContentView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/PhotoPreviewView.swift/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/learnDemoApp.swift/", "<target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>"]}, "P0:target-learnDemo-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,3", "--filter-for-device-os-version", "26.0", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "control-enabled": false, "signature": "577d01f5ae9ff4e5687ecb68382d2a01"}, "P0:target-learnDemo-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "control-enabled": false, "signature": "1b7ef126d7f340e822d9579b7775bb68"}, "P0:target-learnDemo-****************************************************************-:Debug:CompileMetalFile /Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal": {"tool": "shell", "description": "CompileMetalFile /Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/metal", "-c", "-target", "air64-apple-ios18.2-simulator", "-gline-tables-only", "-frecord-sources", "-I/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/include", "-F/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-fmetal-math-mode=fast", "-fmetal-math-fp32-functions=fast", "-serialize-diagnostics", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dia", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "-index-store-path", "/Users/<USER>/Desktop/learnDemo/DerivedData/Index.noindex/DataStore", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat", "/Users/<USER>/Desktop/learnDemo/learnDemo/ColorMatrix.metal"], "env": {"SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk"}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat"], "deps-style": "makefile", "signature": "773e63e5e0f16bc8643ef17fb7ef9bcf"}, "P0:target-learnDemo-****************************************************************-:Debug:CompileXCStrings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ /Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings": {"tool": "shell", "description": "CompileXCStrings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ /Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings", "inputs": ["/Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/xcstringstool", "compile", "--output-directory", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build", "/Users/<USER>/Desktop/learnDemo/InfoPlist.xcstrings"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "2a3bedcfc89fa1c9656a0e16f0d29ba8"}, "P0:target-learnDemo-****************************************************************-:Debug:CompileXCStrings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ /Users/<USER>/Desktop/learnDemo/Localizable.xcstrings": {"tool": "shell", "description": "CompileXCStrings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ /Users/<USER>/Desktop/learnDemo/Localizable.xcstrings", "inputs": ["/Users/<USER>/Desktop/learnDemo/Localizable.xcstrings", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/xcstringstool", "compile", "--output-directory", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build", "/Users/<USER>/Desktop/learnDemo/Localizable.xcstrings"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "4740c7dbb2f7bb5341fd6bb2d7ec7b27"}, "P0:target-learnDemo-****************************************************************-:Debug:CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/InfoPlist.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings": {"tool": "copy-strings-file", "description": "CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/InfoPlist.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/InfoPlist.strings"]}, "P0:target-learnDemo-****************************************************************-:Debug:CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/Localizable.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings": {"tool": "copy-strings-file", "description": "CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/Localizable.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/Localizable.strings"]}, "P0:target-learnDemo-****************************************************************-:Debug:CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/InfoPlist.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings": {"tool": "copy-strings-file", "description": "CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/InfoPlist.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/InfoPlist.strings"]}, "P0:target-learnDemo-****************************************************************-:Debug:CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/Localizable.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings": {"tool": "copy-strings-file", "description": "CopyStringsFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/Localizable.strings /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/Localizable.strings"]}, "P0:target-learnDemo-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "deps": "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-learnDemo-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/CameraView.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/ContentView.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/learnDemoApp.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/PhotoPreviewView.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Metadata.appintents>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/learnDemo.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-learnDemo-****************************************************************--begin-compiling>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/learnDemo.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-learnDemo-****************************************************************--begin-linking>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/learnDemo.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--begin-scanning>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--end": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--entry>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/zh-Hans.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/en.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/en.lproj/Localizable.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/InfoPlist.strings", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/zh-Hans.lproj/Localizable.strings", "<CopySwiftStdlib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Metadata.appintents>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Assets.car", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_signature", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/PkgInfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<Validate /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.hmap", "<target-learnDemo-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-learnDemo-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-learnDemo-****************************************************************--Barrier-ChangePermissions>", "<target-learnDemo-****************************************************************--Barrier-CodeSign>", "<target-learnDemo-****************************************************************--Barrier-CopyAside>", "<target-learnDemo-****************************************************************--Barrier-GenerateStubAPI>", "<target-learnDemo-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-learnDemo-****************************************************************--<PERSON>ier-RegisterProduct>", "<target-learnDemo-****************************************************************--Barrier-StripSymbols>", "<target-learnDemo-****************************************************************--Barrier-Validate>", "<target-learnDemo-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-learnDemo-****************************************************************--DocumentationTaskProducer>", "<target-learnDemo-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-learnDemo-****************************************************************--GeneratedFilesTaskProducer>", "<target-learnDemo-****************************************************************--HeadermapTaskProducer>", "<target-learnDemo-****************************************************************--InfoPlistTaskProducer>", "<target-learnDemo-****************************************************************--ModuleMapTaskProducer>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--ProductPostprocessingTaskProducer>", "<target-learnDemo-****************************************************************--ProductStructureTaskProducer>", "<target-learnDemo-****************************************************************--RealityAssetsTaskProducer>", "<target-learnDemo-****************************************************************--SanitizerTaskProducer>", "<target-learnDemo-****************************************************************--StubBinaryTaskProducer>", "<target-learnDemo-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-learnDemo-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-learnDemo-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-learnDemo-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-learnDemo-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-learnDemo-****************************************************************--TestHostTaskProducer>", "<target-learnDemo-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-learnDemo-****************************************************************--TestTargetTaskProducer>", "<target-learnDemo-****************************************************************--VersionPlistTaskProducer>", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-learnDemo-****************************************************************--generated-headers>", "<target-learnDemo-****************************************************************--swift-generated-headers>"], "outputs": ["<target-learnDemo-****************************************************************--end>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/learnDemo.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-learnDemo-****************************************************************--begin-compiling>"], "outputs": ["<target-learnDemo-****************************************************************--entry>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/learnDemo.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-learnDemo-****************************************************************--immediate>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-learnDemo-****************************************************************--linker-inputs-ready>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h"], "outputs": ["<target-learnDemo-****************************************************************--modules-ready>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.dat", "<CopySwiftStdlib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Metadata.appintents>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Assets.car", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_signature", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<Linked Binary /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList", "<target-learnDemo-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-learnDemo-****************************************************************--unsigned-product-ready>"]}, "P0:target-learnDemo-****************************************************************-:Debug:Gate target-learnDemo-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-learnDemo-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-learnDemo-****************************************************************--will-sign>"]}, "P0:target-learnDemo-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets", "--bundle-identifier", "com.jack.learnDemo", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "control-enabled": false, "signature": "294ff357a80226ef33a94521c0cbffda"}, "P0:target-learnDemo-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/learnDemo/Assets.xcassets/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_signature", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Assets.car"], "deps": "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_dependencies"}, "P0:target-learnDemo-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-learnDemo-****************************************************************-:Debug:MetalLink /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib": {"tool": "shell", "description": "MetalLink /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/metal", "-target", "air64-apple-ios18.2-simulator", "-frecord-sources", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/default.metallib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Metal/ColorMatrix.air"], "env": {"SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk"}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "control-enabled": false, "signature": "7037f142ad4f7f2df4fd945a68e0016a"}, "P0:target-learnDemo-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/thinned>"]}, "P0:target-learnDemo-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_output/unthinned>"]}, "P0:target-learnDemo-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "inputs": ["<target-learnDemo-****************************************************************--start>", "<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "<MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"]}, "P0:target-learnDemo-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/assetcatalog_generated_info.plist", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/PkgInfo"]}, "P0:target-learnDemo-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist", "<target-learnDemo-****************************************************************--ProductStructureTaskProducer>", "<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent"]}, "P0:target-learnDemo-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "<target-learnDemo-****************************************************************--ProductStructureTaskProducer>", "<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "374070b51019f804b5abe70526d60a29"}, "P0:target-learnDemo-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "<target-learnDemo-****************************************************************--Barrier-CodeSign>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"]}, "P0:target-learnDemo-****************************************************************-:Debug:SwiftDriver Compilation learnDemo normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation learnDemo normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/CameraView.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/ContentView.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/learnDemoApp.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/PhotoPreviewView.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-learnDemo-****************************************************************--generated-headers>", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.swiftconstvalues", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-learnDemo-****************************************************************-:Debug:Touch /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "<target-learnDemo-****************************************************************--Barrier-Validate>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "82516d8e52f16e364174d2658b98fa28"}, "P0:target-learnDemo-****************************************************************-:Debug:Validate /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/Info.plist", "<target-learnDemo-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-learnDemo-****************************************************************--will-sign>", "<target-learnDemo-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app>"]}, "P0:target-learnDemo-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/Preview Content", "<target-learnDemo-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo-668b8731326a427b882a522c7f7dd66e-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo-668b8731326a427b882a522c7f7dd66e-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo-668b8731326a427b882a522c7f7dd66e-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-learnDemo-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-learnDemo-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo/", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-learnDemo-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json/", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-learnDemo-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc/", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-learnDemo-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule/", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-learnDemo-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib normal", "inputs": ["<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/learnDemo.debug.dylib", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "43f1cf75d21fe86502e3b8a5aa2c90e1"}, "P2:target-learnDemo-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo normal", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo", "<Linked Binary /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo>", "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "signature": "a5df0b9a69ff8ac7f3c94cd99f272f32"}, "P2:target-learnDemo-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib normal", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/CameraView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemoApp.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/PhotoPreviewView.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.app-Simulated.xcent.der", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "<target-learnDemo-****************************************************************--generated-headers>", "<target-learnDemo-****************************************************************--swift-generated-headers>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "-install_name", "@rpath/learnDemo.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_lto.o", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-fobjc-link-runtime", "-fprofile-instr-generate", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat", "-o", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Products/Debug-iphonesimulator/learnDemo.app/learnDemo.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Desktop/learnDemo", "deps": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_dependency_info.dat"], "deps-style": "dependency-info", "signature": "f3da2e4be059eed493a06a07748f6bbb"}, "P2:target-learnDemo-****************************************************************-:Debug:SwiftDriver Compilation Requirements learnDemo normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements learnDemo normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/learnDemo/learnDemo/CameraView.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/ContentView.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/learnDemoApp.swift", "/Users/<USER>/Desktop/learnDemo/learnDemo/PhotoPreviewView.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/learnDemo/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-learnDemo-****************************************************************--copy-headers-completion>", "<target-learnDemo-****************************************************************--ModuleVerifierTaskProducer>", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftmodule", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftsourceinfo", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.abi.json", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.swiftdoc"]}, "P2:target-learnDemo-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "inputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-Swift.h", "<target-learnDemo-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/learnDemo-Swift.h"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo-OutputFileMap.json"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.LinkFileList"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftConstValuesFileList"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo.SwiftFileList"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/Objects-normal/arm64/learnDemo_const_extract_protocols.json"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/empty-learnDemo.plist"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-DebugDylibPath-normal-arm64.txt"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-non-framework-target-headers.hmap", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-non-framework-target-headers.hmap"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-all-target-headers.hmap"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-generated-files.hmap"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-own-target-headers.hmap"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo-project-headers.hmap"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.DependencyMetadataFileList"]}, "P2:target-learnDemo-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.hmap", "inputs": ["<target-learnDemo-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/learnDemo/DerivedData/Build/Intermediates.noindex/learnDemo.build/Debug-iphonesimulator/learnDemo.build/learnDemo.hmap"]}}}