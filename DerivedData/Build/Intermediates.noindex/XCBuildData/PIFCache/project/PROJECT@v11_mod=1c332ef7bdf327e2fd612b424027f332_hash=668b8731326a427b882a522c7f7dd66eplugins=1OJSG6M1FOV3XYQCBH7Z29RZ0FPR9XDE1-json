{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "INFOPLIST_KEY_NSCameraUsageDescription": "$(NSCameraUsageDescription)", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "668b8731326a427b882a522c7f7dd66e361172d112695bddb7a619a501b54256", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "INFOPLIST_KEY_NSCameraUsageDescription": "$(NSCameraUsageDescription)", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_EMIT_LOC_STRINGS": "YES", "VALIDATE_PRODUCT": "YES"}, "guid": "668b8731326a427b882a522c7f7dd66e1ed4d01085c2517fe7d22a84549126fe", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.json.xcstrings", "guid": "668b8731326a427b882a522c7f7dd66ebbb04c9d230bd05f31e47eda8dd3acf7", "path": "InfoPlist.xcstrings", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json.xcstrings", "guid": "668b8731326a427b882a522c7f7dd66ee83854216ecc476448041791eab14471", "path": "Localizable.xcstrings", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "folder.assetcatalog", "guid": "668b8731326a427b882a522c7f7dd66e56272d38d20f1a58dc506169994fc09e", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66ecfc68448d1bd97cce873d779087eb6e9", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "668b8731326a427b882a522c7f7dd66e895301aa554e4f3f79c45b3a26b2b2d2", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.metal", "guid": "668b8731326a427b882a522c7f7dd66ea2b9b925cdaa8988bbb9b4ec5ffe0380", "path": "ColorMatrix.metal", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e56848db9255974e50cd9569b4f0beaca", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66ef2ef07e0b1af0b901e66c145f0373149", "path": "learnDemoApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e5eaa710a7853f3dca3d8cb21f2cf057b", "path": "PhotoPreviewView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66e4bedbe79badf136255cbe791b3ee18c5", "name": "learnDemo", "path": "learnDemo", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e6dd59b48e4b4646ddc6956f7230061ce", "path": "learnDemoTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66e7a7299c312745cb21a3262f616801049", "name": "learnDemoTests", "path": "learnDemoTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e1fe25e3ea103e0546ca8221aa4eede2c", "path": "CameraUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e42b6ab0b0af6a0c90678484fdcddc0e6", "path": "learnDemoUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66eb6f0ca3bca9107ad368a16ee145486c8", "path": "learnDemoUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66eec58b5c21ae4a4904a7d0c938e4f9b97", "name": "learnDemoUITests", "path": "learnDemoUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "668b8731326a427b882a522c7f7dd66e8902aa440325052c946bb847f38d55f5", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "668b8731326a427b882a522c7f7dd66ec9412e9829117e1dafbc59ec289f8803", "name": "learnDemo", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "668b8731326a427b882a522c7f7dd66e", "path": "/Users/<USER>/Desktop/learnDemo/learnDemo.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/learnDemo", "targets": ["TARGET@v11_hash=c1268fddc62ff384de6c3ad5bc75bfc1", "TARGET@v11_hash=4d2156c0f3819b29d60092f0c62b8399", "TARGET@v11_hash=89b6230f72191f50f132ceca26b9df93"]}