{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "INFOPLIST_KEY_NSCameraUsageDescription": "$(NSCameraUsageDescription)", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "668b8731326a427b882a522c7f7dd66e361172d112695bddb7a619a501b54256", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "INFOPLIST_KEY_NSCameraUsageDescription": "$(NSCameraUsageDescription)", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_EMIT_LOC_STRINGS": "YES", "VALIDATE_PRODUCT": "YES"}, "guid": "668b8731326a427b882a522c7f7dd66e1ed4d01085c2517fe7d22a84549126fe", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.json.xcstrings", "guid": "668b8731326a427b882a522c7f7dd66ebbb04c9d230bd05f31e47eda8dd3acf7", "path": "InfoPlist.xcstrings", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json.xcstrings", "guid": "668b8731326a427b882a522c7f7dd66ee83854216ecc476448041791eab14471", "path": "Localizable.xcstrings", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "folder.assetcatalog", "guid": "668b8731326a427b882a522c7f7dd66e651800047ed240291591d80ca3b33303", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66eafeb43eae1c8a0feac4de69945e2d558", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "668b8731326a427b882a522c7f7dd66ea5260b48ee020e024a4d254582f95909", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e8ed29e5022eccaf4ab07519305030067", "path": "CameraView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.metal", "guid": "668b8731326a427b882a522c7f7dd66eb21f58ff1c6e1d25ce3262719591b9d2", "path": "ColorMatrix.metal", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66ef8379b7bee4d08289423380db924b775", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e10c76dfe4c785888618eca5d7d0e96ba", "path": "learnDemoApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e0201601d1bffff5bbb9641de8290a375", "path": "PhotoPreviewView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66e4bedbe79badf136255cbe791b3ee18c5", "name": "learnDemo", "path": "learnDemo", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e213142a7f0af6b32fc541dcedac08787", "path": "learnDemoTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66e7a7299c312745cb21a3262f616801049", "name": "learnDemoTests", "path": "learnDemoTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66e25f6c200853af589cd9a6dd5f8f30e49", "path": "CameraUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66ed2c0e22d5fc6866ae6f995bfbb894806", "path": "learnDemoUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "668b8731326a427b882a522c7f7dd66ed02f0d4d153d04195862185627d2fc18", "path": "learnDemoUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "668b8731326a427b882a522c7f7dd66eec58b5c21ae4a4904a7d0c938e4f9b97", "name": "learnDemoUITests", "path": "learnDemoUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "668b8731326a427b882a522c7f7dd66e8902aa440325052c946bb847f38d55f5", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "668b8731326a427b882a522c7f7dd66ec9412e9829117e1dafbc59ec289f8803", "name": "learnDemo", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "668b8731326a427b882a522c7f7dd66e", "path": "/Users/<USER>/Desktop/learnDemo/learnDemo.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/learnDemo", "targets": ["TARGET@v11_hash=30e3cede8d49c8ed99247c1d3943ff1a", "TARGET@v11_hash=0d8f537bda6edecf1439d03a7b1924bd", "TARGET@v11_hash=df3cf29b1848a36b3c449d910c3a1b50"]}