<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		m0oA8fU08rd7Tck9dX7CBlx6XXU=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>__preview.dylib</key>
		<data>
		u3RoseDcEvIv8cPSB3gd8fvDp5U=
		</data>
		<key>default.metallib</key>
		<data>
		Pw9tm+06CCoJZUILqVV/n+UzbpI=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LaBrJQFkqRJe0VptfhghS+DFQQE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			06KrpAFlLgrjfMOYQq9+YPnlq8A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>learnDemo.debug.dylib</key>
		<data>
		QlHD4NONa4g1Du1wuq7tFjqXI2Q=
		</data>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Pfc8BgMTFmR5GZvgmLRQGdBaiH4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NUNYz29zMZBXPgfKnw6Krrv7etM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			TRn/I7cTYmQP7piPMpcIFDhD0Wmi++9+V0GiZLiaecM=
			</data>
		</dict>
		<key>default.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			Po40YkHahsclh/KMkBTZCQq0VI6ILNBv8gQ7n1XOQng=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yxOlpda7+Ql74WqHGnPpV3Uq7ndpxaM8Js3RXQfpfuY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3/4iTLneC/LUEB5XKsCCIx3IQhvrN6fFtie0TSj/TQo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>learnDemo.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			v4dz4UC0zAdMOvbisGnOnpuTOCh6J7WSzSq92aUxfgo=
			</data>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cr2eIoawVU5VO2VlEil0OplwwuYI4fu2o+uTAcvGPVg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Qpnv3zOVZqKj29/kHTjqdhX9+KLDFj+HB8vijLrLKDs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
