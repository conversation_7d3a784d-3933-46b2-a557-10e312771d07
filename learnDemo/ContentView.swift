import SwiftUI
import simd

struct ContentView: View {
    // 你提供的颜色矩阵数据
    // 矩阵:
    // (0.10889  0.89111  0.00000)
    // (0.10889  0.89111  0.00000)
    // (0.00447 -0.00447  1.00000)

    // 创建SwiftUI ColorMatrix
    private var customColorMatrix: ColorMatrix {
        // SwiftUI的ColorMatrix使用默认初始化器，然后设置属性
        var matrix = ColorMatrix()

        // 设置你的颜色矩阵值
        // 第一行 (Red输出): 0.10889×R + 0.89111×G + 0.00000×B
        matrix.r1 = 0.10889
        matrix.g1 = 0.89111
        matrix.b1 = 0.00000
        matrix.a1 = 0.0

        // 第二行 (Green输出): 0.10889×R + 0.89111×G + 0.00000×B
        matrix.r2 = 0.10889
        matrix.g2 = 0.89111
        matrix.b2 = 0.00000
        matrix.a2 = 0.0

        // 第三行 (Blue输出): 0.00447×R - 0.00447×G + 1.00000×B
        matrix.r3 = 0.00447
        matrix.g3 = -0.00447
        matrix.b3 = 1.00000
        matrix.a3 = 0.0

        // 第四行 (Alpha输出) - 保持原始alpha
        matrix.r4 = 0.0
        matrix.g4 = 0.0
        matrix.b4 = 0.0
        matrix.a4 = 1.0

        // 第五列 (偏移值)
        matrix.r5 = 0.0
        matrix.g5 = 0.0
        matrix.b5 = 0.0
        matrix.a5 = 0.0

        return matrix
    }

    @State private var isMatrixApplied = false
    var body: some View {
        VStack(spacing: 30) {
            // 标题
            Text("颜色矩阵效果演示")
                .font(.title)
                .fontWeight(.bold)

            // 控制开关
            Toggle("应用颜色矩阵", isOn: $isMatrixApplied)
                .padding(.horizontal)

            // Text视图应用颜色矩阵效果
            VStack(spacing: 20) {
                Text("Text视图颜色矩阵效果")
                    .font(.headline)

                HStack(spacing: 20) {
                    // 原始Text
                    VStack {
                        Text("原始")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        VStack(spacing: 8) {
                            Text("红色文字")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.red)

                            Text("绿色文字")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.green)

                            Text("蓝色文字")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.blue)

                            Text("彩虹渐变")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [.red, .orange, .yellow, .green, .blue, .purple],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        }
                    }

                    // 应用颜色矩阵的Text
                    VStack {
                        Text("颜色矩阵")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        VStack(spacing: 8) {
                            Text("红色文字")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.red)

                            Text("绿色文字")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.green)

                            Text("蓝色文字")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.blue)

                            Text("彩虹渐变")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [.red, .orange, .yellow, .green, .blue, .purple],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        }
                        .opacity(isMatrixApplied ? 0.8 : 1.0)
                        .overlay(
                            // 当启用颜色矩阵时，显示一个模拟效果的覆盖层
                            isMatrixApplied ?
                            VStack(spacing: 8) {
                                Text("红色文字")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color(red: 0.10889 * 1.0 + 0.89111 * 0.0 + 0.00000 * 0.0,
                                                          green: 0.10889 * 1.0 + 0.89111 * 0.0 + 0.00000 * 0.0,
                                                          blue: 0.00447 * 1.0 - 0.00447 * 0.0 + 1.00000 * 0.0))

                                Text("绿色文字")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color(red: 0.10889 * 0.0 + 0.89111 * 1.0 + 0.00000 * 0.0,
                                                          green: 0.10889 * 0.0 + 0.89111 * 1.0 + 0.00000 * 0.0,
                                                          blue: 0.00447 * 0.0 - 0.00447 * 1.0 + 1.00000 * 0.0))

                                Text("蓝色文字")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color(red: 0.10889 * 0.0 + 0.89111 * 0.0 + 0.00000 * 1.0,
                                                          green: 0.10889 * 0.0 + 0.89111 * 0.0 + 0.00000 * 1.0,
                                                          blue: 0.00447 * 0.0 - 0.00447 * 0.0 + 1.00000 * 1.0))

                                Text("彩虹渐变")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundStyle(
                                        LinearGradient(
                                            colors: [
                                                Color(red: 0.10889, green: 0.10889, blue: 0.00447),
                                                Color(red: 0.89111, green: 0.89111, blue: -0.00447),
                                                Color(red: 0.89111, green: 0.89111, blue: -0.00447),
                                                Color(red: 0.89111, green: 0.89111, blue: -0.00447),
                                                Color(red: 0.00000, green: 0.00000, blue: 1.00000),
                                                Color(red: 0.10889, green: 0.10889, blue: 0.00447)
                                            ],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                            } : nil
                        )
                    }
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(15)

            // Canvas绘制的图形效果
            VStack(spacing: 20) {
                Text("Canvas绘制效果")
                    .font(.headline)

                Canvas { context, size in
                    // 如果启用了颜色矩阵，添加滤镜
                    if isMatrixApplied {
                        context.addFilter(.colorMatrix(customColorMatrix))
                    }

                    // 绘制彩色矩形
                    let rectSize: CGFloat = 40
                    let spacing: CGFloat = 8
                    let totalWidth = 4 * rectSize + 3 * spacing
                    let startX = (size.width - totalWidth) / 2
                    let rectY: CGFloat = 20

                    let colors: [Color] = [.red, .green, .blue, .yellow]
                    for (index, color) in colors.enumerated() {
                        let x = startX + CGFloat(index) * (rectSize + spacing)
                        let rect = CGRect(x: x, y: rectY, width: rectSize, height: rectSize)
                        context.fill(Path(rect), with: .color(color))
                    }

                    // 绘制渐变圆形
                    let circleSize: CGFloat = 60
                    let circleRect = CGRect(
                        x: (size.width - circleSize) / 2,
                        y: 80,
                        width: circleSize,
                        height: circleSize
                    )

                    let gradient = Gradient(colors: [.pink, .purple, .blue])
                    context.fill(
                        Path(ellipseIn: circleRect),
                        with: .radialGradient(
                            gradient,
                            center: CGPoint(x: circleRect.midX, y: circleRect.midY),
                            startRadius: 5,
                            endRadius: 30
                        )
                    )
                }
                .frame(height: 160)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
            }

            // 矩阵信息显示
            VStack(alignment: .leading, spacing: 5) {
                Text("当前颜色矩阵:")
                    .font(.headline)

                Text("R: 0.10889×R + 0.89111×G + 0.00000×B")
                    .font(.caption.monospaced())

                Text("G: 0.10889×R + 0.89111×G + 0.00000×B")
                    .font(.caption.monospaced())

                Text("B: 0.00447×R - 0.00447×G + 1.00000×B")
                    .font(.caption.monospaced())
            }
            .padding()
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(10)

            // 说明文字
            Text(isMatrixApplied ? "颜色矩阵已应用" : "颜色矩阵未应用")
                .font(.subheadline)
                .foregroundColor(isMatrixApplied ? .green : .secondary)

            Spacer()
        }
        .padding()
    }
}
